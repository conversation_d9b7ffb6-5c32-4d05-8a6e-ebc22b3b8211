accession,file_size,assembly,biosample_term_name,biosample_type,lab,dataset_accession,download_url,encode_label
ENCFF926KTI,1268446,G<PERSON>h38,K562,cell line,"Will Greenleaf, Stanford",ENCSR859USB,https://www.encodeproject.org/files/ENCFF926KTI/@@download/ENCFF926KTI.bed.gz,K562_1
ENCFF046GBZ,941262,G<PERSON>h38,K562,cell line,"Will Greenleaf, Stanford",ENCSR859USB,https://www.encodeproject.org/files/ENCFF046GBZ/@@download/ENCFF046GBZ.bed.gz,K562_2
ENCFF202HWV,700804,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR859USB,https://www.encodeproject.org/files/ENCFF202HWV/@@download/ENCFF202HWV.bed.gz,K562_3
ENCFF489GQF,1580278,G<PERSON><PERSON>38,K562,cell line,"<PERSON>, Stanford",ENCSR956DNB,https://www.encodeproject.org/files/ENCFF489GQF/@@download/ENCFF489GQF.bed.gz,K562_4
ENCFF540FNK,1259781,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR956DNB,https://www.encodeproject.org/files/ENCFF540FNK/@@download/ENCFF540FNK.bed.gz,K562_5
ENCFF738NOA,848664,GRCh38,K562,cell line,"Will Greenleaf, Stanford",ENCSR956DNB,https://www.encodeproject.org/files/ENCFF738NOA/@@download/ENCFF738NOA.bed.gz,K562_6
ENCFF855OHJ,2777006,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR863TVR,https://www.encodeproject.org/files/ENCFF855OHJ/@@download/ENCFF855OHJ.bed.gz,stim_act_nv_CD8_pos_ab_T__1
ENCFF753NRZ,1337916,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR863TVR,https://www.encodeproject.org/files/ENCFF753NRZ/@@download/ENCFF753NRZ.bed.gz,stim_act_nv_CD8_pos_ab_T__2
ENCFF767EUF,2444596,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR863TVR,https://www.encodeproject.org/files/ENCFF767EUF/@@download/ENCFF767EUF.bed.gz,stim_act_nv_CD8_pos_ab_T__3
ENCFF767YBU,3145005,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR830ALP,https://www.encodeproject.org/files/ENCFF767YBU/@@download/ENCFF767YBU.bed.gz,activated_T_cell_1
ENCFF847WTI,2493144,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR830ALP,https://www.encodeproject.org/files/ENCFF847WTI/@@download/ENCFF847WTI.bed.gz,activated_T_cell_2
ENCFF564OCG,2543233,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR830ALP,https://www.encodeproject.org/files/ENCFF564OCG/@@download/ENCFF564OCG.bed.gz,activated_T_cell_3
ENCFF999QSA,1011845,GRCh38,T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR803FKU,https://www.encodeproject.org/files/ENCFF999QSA/@@download/ENCFF999QSA.bed.gz,T_helper_17_cell_1
ENCFF870WGS,3259147,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR637OPZ,https://www.encodeproject.org/files/ENCFF870WGS/@@download/ENCFF870WGS.bed.gz,stim_act_nv_CD8_pos_ab_T__4
ENCFF865ZYY,2626440,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR637OPZ,https://www.encodeproject.org/files/ENCFF865ZYY/@@download/ENCFF865ZYY.bed.gz,stim_act_nv_CD8_pos_ab_T__5
ENCFF701COP,2583748,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR637OPZ,https://www.encodeproject.org/files/ENCFF701COP/@@download/ENCFF701COP.bed.gz,stim_act_nv_CD8_pos_ab_T__6
ENCFF273AWR,2160613,GRCh38,"naive thymus-derived CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR614JAG,https://www.encodeproject.org/files/ENCFF273AWR/@@download/ENCFF273AWR.bed.gz,nv_thy_CD8_pos_ab_T__1
ENCFF038ONH,1872245,GRCh38,"naive thymus-derived CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR614JAG,https://www.encodeproject.org/files/ENCFF038ONH/@@download/ENCFF038ONH.bed.gz,nv_thy_CD8_pos_ab_T__2
ENCFF246EKP,1740384,GRCh38,"naive thymus-derived CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR614JAG,https://www.encodeproject.org/files/ENCFF246EKP/@@download/ENCFF246EKP.bed.gz,nv_thy_CD8_pos_ab_T__3
ENCFF514SFC,2238784,GRCh38,T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR611BQR,https://www.encodeproject.org/files/ENCFF514SFC/@@download/ENCFF514SFC.bed.gz,T_helper_17_cell_2
ENCFF679WGA,1892366,GRCh38,T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR611BQR,https://www.encodeproject.org/files/ENCFF679WGA/@@download/ENCFF679WGA.bed.gz,T_helper_17_cell_3
ENCFF980FDH,1768654,GRCh38,T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR611BQR,https://www.encodeproject.org/files/ENCFF980FDH/@@download/ENCFF980FDH.bed.gz,T_helper_17_cell_4
ENCFF242BPO,1009173,GRCh38,"naive thymus-derived CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR513EVP,https://www.encodeproject.org/files/ENCFF242BPO/@@download/ENCFF242BPO.bed.gz,nv_thy_CD8_pos_ab_T__4
ENCFF284FAV,1244971,GRCh38,"CD4-positive, CD25-positive, alpha-beta regulatory T cell",primary cell,"Michael Snyder, Stanford",ENCSR504OUW,https://www.encodeproject.org/files/ENCFF284FAV/@@download/ENCFF284FAV.bed.gz,CD4_pos_CD25_pos_ab_regul_1
ENCFF852LGZ,1987941,GRCh38,"activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR477YSU,https://www.encodeproject.org/files/ENCFF852LGZ/@@download/ENCFF852LGZ.bed.gz,activated_nv_CD8_pos_ab_T_1
ENCFF788CFY,2259172,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR452COS,https://www.encodeproject.org/files/ENCFF788CFY/@@download/ENCFF788CFY.bed.gz,nv_thy_CD4_pos_ab_T__1
ENCFF400WXD,1711125,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR452COS,https://www.encodeproject.org/files/ENCFF400WXD/@@download/ENCFF400WXD.bed.gz,nv_thy_CD4_pos_ab_T__2
ENCFF759ZEZ,1749927,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR452COS,https://www.encodeproject.org/files/ENCFF759ZEZ/@@download/ENCFF759ZEZ.bed.gz,nv_thy_CD4_pos_ab_T__3
ENCFF717CDB,2589193,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR373NFA,https://www.encodeproject.org/files/ENCFF717CDB/@@download/ENCFF717CDB.bed.gz,activated_nv_CD4_pos_ab_T_1
ENCFF961DWF,2093428,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR373NFA,https://www.encodeproject.org/files/ENCFF961DWF/@@download/ENCFF961DWF.bed.gz,activated_nv_CD4_pos_ab_T_2
ENCFF272RDL,2148959,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR373NFA,https://www.encodeproject.org/files/ENCFF272RDL/@@download/ENCFF272RDL.bed.gz,activated_nv_CD4_pos_ab_T_3
ENCFF331XQA,2030809,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335XRZ,https://www.encodeproject.org/files/ENCFF331XQA/@@download/ENCFF331XQA.bed.gz,stim_act_effector_memory__1
ENCFF816USW,1512891,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335XRZ,https://www.encodeproject.org/files/ENCFF816USW/@@download/ENCFF816USW.bed.gz,stim_act_effector_memory__2
ENCFF276FVU,1326091,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335XRZ,https://www.encodeproject.org/files/ENCFF276FVU/@@download/ENCFF276FVU.bed.gz,stim_act_effector_memory__3
ENCFF081RHG,1854895,GRCh38,"stimulated activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR326ESM,https://www.encodeproject.org/files/ENCFF081RHG/@@download/ENCFF081RHG.bed.gz,stim_act_nv_CD8_pos_ab_T__7
ENCFF939PQC,1818460,GRCh38,"activated CD4-positive, CD25-positive, alpha-beta regulatory T cell",primary cell,"Michael Snyder, Stanford",ENCSR261PWP,https://www.encodeproject.org/files/ENCFF939PQC/@@download/ENCFF939PQC.bed.gz,activated_CD4_pos_CD25_po_1
ENCFF518VXK,955681,GRCh38,T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR248ZAC,https://www.encodeproject.org/files/ENCFF518VXK/@@download/ENCFF518VXK.bed.gz,T_helper_17_cell_5
ENCFF907HEI,3223732,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR174SUM,https://www.encodeproject.org/files/ENCFF907HEI/@@download/ENCFF907HEI.bed.gz,activated_nv_CD4_pos_ab_T_4
ENCFF338BCO,2502315,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR174SUM,https://www.encodeproject.org/files/ENCFF338BCO/@@download/ENCFF338BCO.bed.gz,activated_nv_CD4_pos_ab_T_5
ENCFF029EIQ,2636290,GRCh38,"activated naive CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR174SUM,https://www.encodeproject.org/files/ENCFF029EIQ/@@download/ENCFF029EIQ.bed.gz,activated_nv_CD4_pos_ab_T_6
ENCFF270DPY,1472645,GRCh38,"central memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR165FYY,https://www.encodeproject.org/files/ENCFF270DPY/@@download/ENCFF270DPY.bed.gz,central_memory_CD8_pos_ab_1
ENCFF016CIJ,1320140,GRCh38,"central memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR165FYY,https://www.encodeproject.org/files/ENCFF016CIJ/@@download/ENCFF016CIJ.bed.gz,central_memory_CD8_pos_ab_2
ENCFF441BUL,920355,GRCh38,"central memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR165FYY,https://www.encodeproject.org/files/ENCFF441BUL/@@download/ENCFF441BUL.bed.gz,central_memory_CD8_pos_ab_3
ENCFF519QMA,1368815,GRCh38,"CD4-positive, CD25-positive, alpha-beta regulatory T cell",primary cell,"Michael Snyder, Stanford",ENCSR159GFS,https://www.encodeproject.org/files/ENCFF519QMA/@@download/ENCFF519QMA.bed.gz,CD4_pos_CD25_pos_ab_regul_2
ENCFF846LCD,3184036,GRCh38,activated T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR115OIV,https://www.encodeproject.org/files/ENCFF846LCD/@@download/ENCFF846LCD.bed.gz,activated_T_helper_17__1
ENCFF168ZRB,2645151,GRCh38,activated T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR115OIV,https://www.encodeproject.org/files/ENCFF168ZRB/@@download/ENCFF168ZRB.bed.gz,activated_T_helper_17__2
ENCFF217QDP,2560568,GRCh38,activated T-helper 17 cell,primary cell,"Michael Snyder, Stanford",ENCSR115OIV,https://www.encodeproject.org/files/ENCFF217QDP/@@download/ENCFF217QDP.bed.gz,activated_T_helper_17__3
ENCFF603GBI,1304821,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR019XCN,https://www.encodeproject.org/files/ENCFF603GBI/@@download/ENCFF603GBI.bed.gz,effector_memory_CD8_pos_a_1
ENCFF205RXD,1104818,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR019XCN,https://www.encodeproject.org/files/ENCFF205RXD/@@download/ENCFF205RXD.bed.gz,effector_memory_CD8_pos_a_2
ENCFF589DMP,912846,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR019XCN,https://www.encodeproject.org/files/ENCFF589DMP/@@download/ENCFF589DMP.bed.gz,effector_memory_CD8_pos_a_3
ENCFF928YBF,3375513,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR998BBI,https://www.encodeproject.org/files/ENCFF928YBF/@@download/ENCFF928YBF.bed.gz,HCT116_1
ENCFF109DYC,2777006,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR998BBI,https://www.encodeproject.org/files/ENCFF109DYC/@@download/ENCFF109DYC.bed.gz,HCT116_2
ENCFF246CQU,2621314,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR998BBI,https://www.encodeproject.org/files/ENCFF246CQU/@@download/ENCFF246CQU.bed.gz,HCT116_3
ENCFF153VAJ,2836442,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR988ITF,https://www.encodeproject.org/files/ENCFF153VAJ/@@download/ENCFF153VAJ.bed.gz,HCT116_4
ENCFF647VWG,2311490,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR988ITF,https://www.encodeproject.org/files/ENCFF647VWG/@@download/ENCFF647VWG.bed.gz,HCT116_5
ENCFF574SCZ,2294378,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR988ITF,https://www.encodeproject.org/files/ENCFF574SCZ/@@download/ENCFF574SCZ.bed.gz,HCT116_6
ENCFF867UIK,3881914,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR874GXS,https://www.encodeproject.org/files/ENCFF867UIK/@@download/ENCFF867UIK.bed.gz,HCT116_7
ENCFF191ZBE,3199623,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR874GXS,https://www.encodeproject.org/files/ENCFF191ZBE/@@download/ENCFF191ZBE.bed.gz,HCT116_8
ENCFF606QPO,3056539,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR874GXS,https://www.encodeproject.org/files/ENCFF606QPO/@@download/ENCFF606QPO.bed.gz,HCT116_9
ENCFF311OOH,3552026,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR816PPJ,https://www.encodeproject.org/files/ENCFF311OOH/@@download/ENCFF311OOH.bed.gz,HCT116_10
ENCFF492FZV,2661963,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR816PPJ,https://www.encodeproject.org/files/ENCFF492FZV/@@download/ENCFF492FZV.bed.gz,HCT116_11
ENCFF073MUV,3017645,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR816PPJ,https://www.encodeproject.org/files/ENCFF073MUV/@@download/ENCFF073MUV.bed.gz,HCT116_12
ENCFF370EDY,3434194,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR780PLN,https://www.encodeproject.org/files/ENCFF370EDY/@@download/ENCFF370EDY.bed.gz,HCT116_13
ENCFF389NUT,2706964,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR780PLN,https://www.encodeproject.org/files/ENCFF389NUT/@@download/ENCFF389NUT.bed.gz,HCT116_14
ENCFF958VRD,2726030,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR780PLN,https://www.encodeproject.org/files/ENCFF958VRD/@@download/ENCFF958VRD.bed.gz,HCT116_15
ENCFF679NSE,3650159,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR477YZA,https://www.encodeproject.org/files/ENCFF679NSE/@@download/ENCFF679NSE.bed.gz,HCT116_16
ENCFF257DMN,2904622,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR477YZA,https://www.encodeproject.org/files/ENCFF257DMN/@@download/ENCFF257DMN.bed.gz,HCT116_17
ENCFF209UDP,2927296,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR477YZA,https://www.encodeproject.org/files/ENCFF209UDP/@@download/ENCFF209UDP.bed.gz,HCT116_18
ENCFF692CRN,3544986,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR445VYQ,https://www.encodeproject.org/files/ENCFF692CRN/@@download/ENCFF692CRN.bed.gz,HCT116_19
ENCFF408VJA,2869916,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR445VYQ,https://www.encodeproject.org/files/ENCFF408VJA/@@download/ENCFF408VJA.bed.gz,HCT116_20
ENCFF902OZN,2862039,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR445VYQ,https://www.encodeproject.org/files/ENCFF902OZN/@@download/ENCFF902OZN.bed.gz,HCT116_21
ENCFF660PIO,3611400,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR441BDP,https://www.encodeproject.org/files/ENCFF660PIO/@@download/ENCFF660PIO.bed.gz,HCT116_22
ENCFF959FQW,2845393,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR441BDP,https://www.encodeproject.org/files/ENCFF959FQW/@@download/ENCFF959FQW.bed.gz,HCT116_23
ENCFF409NYB,2903475,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR441BDP,https://www.encodeproject.org/files/ENCFF409NYB/@@download/ENCFF409NYB.bed.gz,HCT116_24
ENCFF628DWR,3989767,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR389WJO,https://www.encodeproject.org/files/ENCFF628DWR/@@download/ENCFF628DWR.bed.gz,HCT116_25
ENCFF338EFN,3288933,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR389WJO,https://www.encodeproject.org/files/ENCFF338EFN/@@download/ENCFF338EFN.bed.gz,HCT116_26
ENCFF500OLT,3315186,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR389WJO,https://www.encodeproject.org/files/ENCFF500OLT/@@download/ENCFF500OLT.bed.gz,HCT116_27
ENCFF155NTI,3258243,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR345XUN,https://www.encodeproject.org/files/ENCFF155NTI/@@download/ENCFF155NTI.bed.gz,HCT116_28
ENCFF035BSF,2658082,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR345XUN,https://www.encodeproject.org/files/ENCFF035BSF/@@download/ENCFF035BSF.bed.gz,HCT116_29
ENCFF628WZB,2540283,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR345XUN,https://www.encodeproject.org/files/ENCFF628WZB/@@download/ENCFF628WZB.bed.gz,HCT116_30
ENCFF321FBR,3087705,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR328JGW,https://www.encodeproject.org/files/ENCFF321FBR/@@download/ENCFF321FBR.bed.gz,HCT116_31
ENCFF933TYP,2339733,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR328JGW,https://www.encodeproject.org/files/ENCFF933TYP/@@download/ENCFF933TYP.bed.gz,HCT116_32
ENCFF640GNQ,2465878,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR328JGW,https://www.encodeproject.org/files/ENCFF640GNQ/@@download/ENCFF640GNQ.bed.gz,HCT116_33
ENCFF847COV,2836413,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR302NVX,https://www.encodeproject.org/files/ENCFF847COV/@@download/ENCFF847COV.bed.gz,HCT116_34
ENCFF505PFO,1901755,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR302NVX,https://www.encodeproject.org/files/ENCFF505PFO/@@download/ENCFF505PFO.bed.gz,HCT116_35
ENCFF379NOI,2854565,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR302NVX,https://www.encodeproject.org/files/ENCFF379NOI/@@download/ENCFF379NOI.bed.gz,HCT116_36
ENCFF473QDC,2569523,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR260SWI,https://www.encodeproject.org/files/ENCFF473QDC/@@download/ENCFF473QDC.bed.gz,HCT116_37
ENCFF197IGM,2127948,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR260SWI,https://www.encodeproject.org/files/ENCFF197IGM/@@download/ENCFF197IGM.bed.gz,HCT116_38
ENCFF423FZD,2022538,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR260SWI,https://www.encodeproject.org/files/ENCFF423FZD/@@download/ENCFF423FZD.bed.gz,HCT116_39
ENCFF369SDE,3361247,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR182JAI,https://www.encodeproject.org/files/ENCFF369SDE/@@download/ENCFF369SDE.bed.gz,HCT116_40
ENCFF751WEU,2739889,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR182JAI,https://www.encodeproject.org/files/ENCFF751WEU/@@download/ENCFF751WEU.bed.gz,HCT116_41
ENCFF133CQF,2983877,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR182JAI,https://www.encodeproject.org/files/ENCFF133CQF/@@download/ENCFF133CQF.bed.gz,HCT116_42
ENCFF420AQB,3981535,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR135OML,https://www.encodeproject.org/files/ENCFF420AQB/@@download/ENCFF420AQB.bed.gz,HCT116_43
ENCFF676FLS,3181364,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR135OML,https://www.encodeproject.org/files/ENCFF676FLS/@@download/ENCFF676FLS.bed.gz,HCT116_44
ENCFF593ZFH,3179032,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR135OML,https://www.encodeproject.org/files/ENCFF593ZFH/@@download/ENCFF593ZFH.bed.gz,HCT116_45
ENCFF232YAJ,1662062,GRCh38,"activated CD4-positive, CD25-positive, alpha-beta regulatory T cell",primary cell,"Michael Snyder, Stanford",ENCSR094QFZ,https://www.encodeproject.org/files/ENCFF232YAJ/@@download/ENCFF232YAJ.bed.gz,activated_CD4_pos_CD25_po_2
ENCFF809MNF,3254022,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR059BAR,https://www.encodeproject.org/files/ENCFF809MNF/@@download/ENCFF809MNF.bed.gz,HCT116_46
ENCFF706KCD,2771152,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR059BAR,https://www.encodeproject.org/files/ENCFF706KCD/@@download/ENCFF706KCD.bed.gz,HCT116_47
ENCFF469HQE,2684751,GRCh38,HCT116,cell line,"Michael Snyder, Stanford",ENCSR059BAR,https://www.encodeproject.org/files/ENCFF469HQE/@@download/ENCFF469HQE.bed.gz,HCT116_48
ENCFF375TLF,2297698,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR977LVI,https://www.encodeproject.org/files/ENCFF375TLF/@@download/ENCFF375TLF.bed.gz,T_cell_1
ENCFF072PLZ,1879490,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR977LVI,https://www.encodeproject.org/files/ENCFF072PLZ/@@download/ENCFF072PLZ.bed.gz,T_cell_2
ENCFF753XDV,1943888,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR977LVI,https://www.encodeproject.org/files/ENCFF753XDV/@@download/ENCFF753XDV.bed.gz,T_cell_3
ENCFF358TXK,2193470,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR903WVU,https://www.encodeproject.org/files/ENCFF358TXK/@@download/ENCFF358TXK.bed.gz,naive_B_cell_1
ENCFF956UKN,1773879,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR903WVU,https://www.encodeproject.org/files/ENCFF956UKN/@@download/ENCFF956UKN.bed.gz,naive_B_cell_2
ENCFF590QLY,1864123,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR903WVU,https://www.encodeproject.org/files/ENCFF590QLY/@@download/ENCFF590QLY.bed.gz,naive_B_cell_3
ENCFF646NJM,2159434,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR854TTM,https://www.encodeproject.org/files/ENCFF646NJM/@@download/ENCFF646NJM.bed.gz,natural_killer_cell_1
ENCFF859WWL,1656787,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR854TTM,https://www.encodeproject.org/files/ENCFF859WWL/@@download/ENCFF859WWL.bed.gz,natural_killer_cell_2
ENCFF286EBA,1761437,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR854TTM,https://www.encodeproject.org/files/ENCFF286EBA/@@download/ENCFF286EBA.bed.gz,natural_killer_cell_3
ENCFF947LSQ,1955031,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR808HWS,https://www.encodeproject.org/files/ENCFF947LSQ/@@download/ENCFF947LSQ.bed.gz,natural_killer_cell_4
ENCFF704BAI,1679152,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR808HWS,https://www.encodeproject.org/files/ENCFF704BAI/@@download/ENCFF704BAI.bed.gz,natural_killer_cell_5
ENCFF507PGF,1404546,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR808HWS,https://www.encodeproject.org/files/ENCFF507PGF/@@download/ENCFF507PGF.bed.gz,natural_killer_cell_6
ENCFF219TJQ,2072378,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR685OFR,https://www.encodeproject.org/files/ENCFF219TJQ/@@download/ENCFF219TJQ.bed.gz,naive_B_cell_4
ENCFF950NID,1791657,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR685OFR,https://www.encodeproject.org/files/ENCFF950NID/@@download/ENCFF950NID.bed.gz,naive_B_cell_5
ENCFF380TOL,1729148,GRCh38,naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR685OFR,https://www.encodeproject.org/files/ENCFF380TOL/@@download/ENCFF380TOL.bed.gz,naive_B_cell_6
ENCFF045RJR,3572625,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR653VSR,https://www.encodeproject.org/files/ENCFF045RJR/@@download/ENCFF045RJR.bed.gz,stim_act_nv_B__1
ENCFF922ICC,2999607,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR653VSR,https://www.encodeproject.org/files/ENCFF922ICC/@@download/ENCFF922ICC.bed.gz,stim_act_nv_B__2
ENCFF530QPE,2654486,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR653VSR,https://www.encodeproject.org/files/ENCFF530QPE/@@download/ENCFF530QPE.bed.gz,stim_act_nv_B__3
ENCFF628FAP,2331016,GRCh38,memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR610AQP,https://www.encodeproject.org/files/ENCFF628FAP/@@download/ENCFF628FAP.bed.gz,memory_B_cell_1
ENCFF883EEJ,1931743,GRCh38,memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR610AQP,https://www.encodeproject.org/files/ENCFF883EEJ/@@download/ENCFF883EEJ.bed.gz,memory_B_cell_2
ENCFF881FMP,1890802,GRCh38,memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR610AQP,https://www.encodeproject.org/files/ENCFF881FMP/@@download/ENCFF881FMP.bed.gz,memory_B_cell_3
ENCFF569WGY,2880863,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR558ZSN,https://www.encodeproject.org/files/ENCFF569WGY/@@download/ENCFF569WGY.bed.gz,activated_T_cell_4
ENCFF837BSF,2373465,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR558ZSN,https://www.encodeproject.org/files/ENCFF837BSF/@@download/ENCFF837BSF.bed.gz,activated_T_cell_5
ENCFF521UEK,2228711,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR558ZSN,https://www.encodeproject.org/files/ENCFF521UEK/@@download/ENCFF521UEK.bed.gz,activated_T_cell_6
ENCFF743FTR,2095512,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR392YGP,https://www.encodeproject.org/files/ENCFF743FTR/@@download/ENCFF743FTR.bed.gz,effector_memory_CD8_pos_a_4
ENCFF244HGF,1836831,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR392YGP,https://www.encodeproject.org/files/ENCFF244HGF/@@download/ENCFF244HGF.bed.gz,effector_memory_CD8_pos_a_5
ENCFF876PGP,1706942,GRCh38,"effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR392YGP,https://www.encodeproject.org/files/ENCFF876PGP/@@download/ENCFF876PGP.bed.gz,effector_memory_CD8_pos_a_6
ENCFF031HYC,3227957,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR379NMT,https://www.encodeproject.org/files/ENCFF031HYC/@@download/ENCFF031HYC.bed.gz,stim_act_nv_B__4
ENCFF721ADO,2486599,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR379NMT,https://www.encodeproject.org/files/ENCFF721ADO/@@download/ENCFF721ADO.bed.gz,stim_act_nv_B__5
ENCFF899EJR,2598525,GRCh38,stimulated activated naive B cell,primary cell,"Michael Snyder, Stanford",ENCSR379NMT,https://www.encodeproject.org/files/ENCFF899EJR/@@download/ENCFF899EJR.bed.gz,stim_act_nv_B__6
ENCFF007XEZ,3061123,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335LHS,https://www.encodeproject.org/files/ENCFF007XEZ/@@download/ENCFF007XEZ.bed.gz,stim_act_effector_memory__4
ENCFF394AHS,2583196,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335LHS,https://www.encodeproject.org/files/ENCFF394AHS/@@download/ENCFF394AHS.bed.gz,stim_act_effector_memory__5
ENCFF767JJK,2671206,GRCh38,"stimulated activated effector memory CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR335LHS,https://www.encodeproject.org/files/ENCFF767JJK/@@download/ENCFF767JJK.bed.gz,stim_act_effector_memory__6
ENCFF024NFM,1742310,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR305QTE,https://www.encodeproject.org/files/ENCFF024NFM/@@download/ENCFF024NFM.bed.gz,natural_killer_cell_7
ENCFF367LOM,1193627,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR305QTE,https://www.encodeproject.org/files/ENCFF367LOM/@@download/ENCFF367LOM.bed.gz,natural_killer_cell_8
ENCFF961WQE,1312800,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR305QTE,https://www.encodeproject.org/files/ENCFF961WQE/@@download/ENCFF961WQE.bed.gz,natural_killer_cell_9
ENCFF141IXH,2324350,GRCh38,stimulated activated memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR302PTB,https://www.encodeproject.org/files/ENCFF141IXH/@@download/ENCFF141IXH.bed.gz,stim_act_memory_B__1
ENCFF713OLR,2000977,GRCh38,stimulated activated memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR302PTB,https://www.encodeproject.org/files/ENCFF713OLR/@@download/ENCFF713OLR.bed.gz,stim_act_memory_B__2
ENCFF369PGW,1794744,GRCh38,stimulated activated memory B cell,primary cell,"Michael Snyder, Stanford",ENCSR302PTB,https://www.encodeproject.org/files/ENCFF369PGW/@@download/ENCFF369PGW.bed.gz,stim_act_memory_B__3
ENCFF513JVX,2211757,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR260LAN,https://www.encodeproject.org/files/ENCFF513JVX/@@download/ENCFF513JVX.bed.gz,nv_thy_CD4_pos_ab_T__4
ENCFF199DOD,1835235,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR260LAN,https://www.encodeproject.org/files/ENCFF199DOD/@@download/ENCFF199DOD.bed.gz,nv_thy_CD4_pos_ab_T__5
ENCFF400SHU,1820693,GRCh38,"naive thymus-derived CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR260LAN,https://www.encodeproject.org/files/ENCFF400SHU/@@download/ENCFF400SHU.bed.gz,nv_thy_CD4_pos_ab_T__6
ENCFF774APK,2438689,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR044ATC,https://www.encodeproject.org/files/ENCFF774APK/@@download/ENCFF774APK.bed.gz,natural_killer_cell_10
ENCFF450XPR,2057916,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR044ATC,https://www.encodeproject.org/files/ENCFF450XPR/@@download/ENCFF450XPR.bed.gz,natural_killer_cell_11
ENCFF473MMF,2000049,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR044ATC,https://www.encodeproject.org/files/ENCFF473MMF/@@download/ENCFF473MMF.bed.gz,natural_killer_cell_12
ENCFF206PEF,36851,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR032MEH,https://www.encodeproject.org/files/ENCFF206PEF/@@download/ENCFF206PEF.bed.gz,T_cell_4
ENCFF374FLX,11966,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR032MEH,https://www.encodeproject.org/files/ENCFF374FLX/@@download/ENCFF374FLX.bed.gz,T_cell_5
ENCFF195XAI,20644,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR032MEH,https://www.encodeproject.org/files/ENCFF195XAI/@@download/ENCFF195XAI.bed.gz,T_cell_6
ENCFF316PNL,2103093,GRCh38,"naive thymus-derived CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR283LPH,https://www.encodeproject.org/files/ENCFF316PNL/@@download/ENCFF316PNL.bed.gz,nv_thy_CD8_pos_ab_T__5
ENCFF960LQQ,2105737,GRCh38,"activated CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR545UJP,https://www.encodeproject.org/files/ENCFF960LQQ/@@download/ENCFF960LQQ.bed.gz,activated_CD8_pos_ab_T__1
ENCFF311WRF,2147154,GRCh38,"activated CD8-positive, alpha-beta memory T cell",primary cell,"Michael Snyder, Stanford",ENCSR915MTG,https://www.encodeproject.org/files/ENCFF311WRF/@@download/ENCFF311WRF.bed.gz,activated_CD8_pos_ab_memo_1
ENCFF595GOY,1893985,GRCh38,"activated naive CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR983HXF,https://www.encodeproject.org/files/ENCFF595GOY/@@download/ENCFF595GOY.bed.gz,activated_nv_CD8_pos_ab_T_2
ENCFF201BYH,1827771,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR092SRS,https://www.encodeproject.org/files/ENCFF201BYH/@@download/ENCFF201BYH.bed.gz,activated_T_cell_7
ENCFF170XRK,1963064,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR102RXG,https://www.encodeproject.org/files/ENCFF170XRK/@@download/ENCFF170XRK.bed.gz,T_cell_7
ENCFF100NUL,1866703,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR258RSH,https://www.encodeproject.org/files/ENCFF100NUL/@@download/ENCFF100NUL.bed.gz,T_cell_8
ENCFF904ZLF,1614068,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR299LSN,https://www.encodeproject.org/files/ENCFF904ZLF/@@download/ENCFF904ZLF.bed.gz,T_cell_9
ENCFF928JLQ,1951307,GRCh38,natural killer cell,primary cell,"Michael Snyder, Stanford",ENCSR373GMM,https://www.encodeproject.org/files/ENCFF928JLQ/@@download/ENCFF928JLQ.bed.gz,natural_killer_cell_13
ENCFF590CQU,2206454,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR411NFB,https://www.encodeproject.org/files/ENCFF590CQU/@@download/ENCFF590CQU.bed.gz,activated_T_cell_8
ENCFF230RRP,1763252,GRCh38,"CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR476VJY,https://www.encodeproject.org/files/ENCFF230RRP/@@download/ENCFF230RRP.bed.gz,CD8_pos_ab_T_cell_1
ENCFF492GYV,3771210,GRCh38,WTC11,cell line,"Michael Snyder, Stanford",ENCSR541KFY,https://www.encodeproject.org/files/ENCFF492GYV/@@download/ENCFF492GYV.bed.gz,WTC11_1
ENCFF262EMM,3113991,GRCh38,WTC11,cell line,"Michael Snyder, Stanford",ENCSR541KFY,https://www.encodeproject.org/files/ENCFF262EMM/@@download/ENCFF262EMM.bed.gz,WTC11_2
ENCFF770QET,3083237,GRCh38,WTC11,cell line,"Michael Snyder, Stanford",ENCSR541KFY,https://www.encodeproject.org/files/ENCFF770QET/@@download/ENCFF770QET.bed.gz,WTC11_3
ENCFF390ASB,1616466,GRCh38,B cell,primary cell,"Michael Snyder, Stanford",ENCSR603LVR,https://www.encodeproject.org/files/ENCFF390ASB/@@download/ENCFF390ASB.bed.gz,B_cell_1
ENCFF490FII,1910266,GRCh38,activated B cell,primary cell,"Michael Snyder, Stanford",ENCSR659SFK,https://www.encodeproject.org/files/ENCFF490FII/@@download/ENCFF490FII.bed.gz,activated_B_cell_1
ENCFF330FRH,2544902,GRCh38,"activated CD8-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR718HWW,https://www.encodeproject.org/files/ENCFF330FRH/@@download/ENCFF330FRH.bed.gz,activated_CD8_pos_ab_T__2
ENCFF760LQQ,1459975,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR775KMA,https://www.encodeproject.org/files/ENCFF760LQQ/@@download/ENCFF760LQQ.bed.gz,activated_T_cell_9
ENCFF944LFH,1835417,GRCh38,"CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR841LHT,https://www.encodeproject.org/files/ENCFF944LFH/@@download/ENCFF944LFH.bed.gz,CD4_pos_ab_T_cell_1
ENCFF873YXD,1573222,GRCh38,T-cell,primary cell,"Michael Snyder, Stanford",ENCSR934AWQ,https://www.encodeproject.org/files/ENCFF873YXD/@@download/ENCFF873YXD.bed.gz,T_cell_10
ENCFF464QZE,2690212,GRCh38,"activated CD4-positive, alpha-beta T cell",primary cell,"Michael Snyder, Stanford",ENCSR991PBP,https://www.encodeproject.org/files/ENCFF464QZE/@@download/ENCFF464QZE.bed.gz,activated_CD4_pos_ab_T__1
ENCFF523YFW,2086255,GRCh38,activated T-cell,primary cell,"Michael Snyder, Stanford",ENCSR993XED,https://www.encodeproject.org/files/ENCFF523YFW/@@download/ENCFF523YFW.bed.gz,activated_T_cell_10
ENCFF221FSW,224631,GRCh38,posterior cingulate cortex,tissue,"Barbara Wold, Caltech",ENCSR729FNL,https://www.encodeproject.org/files/ENCFF221FSW/@@download/ENCFF221FSW.bed.gz,posterior_cingulate_corte_1
ENCFF858CCP,513872,GRCh38,cerebellum,tissue,"Barbara Wold, Caltech",ENCSR802GEV,https://www.encodeproject.org/files/ENCFF858CCP/@@download/ENCFF858CCP.bed.gz,cerebellum_1
ENCFF559CCG,2328019,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR059IHA,https://www.encodeproject.org/files/ENCFF559CCG/@@download/ENCFF559CCG.bed.gz,dendritic_cell_1
ENCFF500UNC,1806551,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR131CHJ,https://www.encodeproject.org/files/ENCFF500UNC/@@download/ENCFF500UNC.bed.gz,dendritic_cell_2
ENCFF145UGV,1208229,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR156FDX,https://www.encodeproject.org/files/ENCFF145UGV/@@download/ENCFF145UGV.bed.gz,dendritic_cell_3
ENCFF406CMK,1906186,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR204FCV,https://www.encodeproject.org/files/ENCFF406CMK/@@download/ENCFF406CMK.bed.gz,dendritic_cell_4
ENCFF591TRF,2698876,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR208NBF,https://www.encodeproject.org/files/ENCFF591TRF/@@download/ENCFF591TRF.bed.gz,dendritic_cell_5
ENCFF195JHT,1354869,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR222SCA,https://www.encodeproject.org/files/ENCFF195JHT/@@download/ENCFF195JHT.bed.gz,dendritic_cell_6
ENCFF205VUK,1706126,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR237VSF,https://www.encodeproject.org/files/ENCFF205VUK/@@download/ENCFF205VUK.bed.gz,dendritic_cell_7
ENCFF223CMS,1087848,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR290AUK,https://www.encodeproject.org/files/ENCFF223CMS/@@download/ENCFF223CMS.bed.gz,dendritic_cell_8
ENCFF264XMH,1777603,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR329XFZ,https://www.encodeproject.org/files/ENCFF264XMH/@@download/ENCFF264XMH.bed.gz,dendritic_cell_9
ENCFF108OXE,1680542,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR370QWN,https://www.encodeproject.org/files/ENCFF108OXE/@@download/ENCFF108OXE.bed.gz,dendritic_cell_10
ENCFF382JJS,3025875,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR459AYS,https://www.encodeproject.org/files/ENCFF382JJS/@@download/ENCFF382JJS.bed.gz,dendritic_cell_11
ENCFF086PSD,1899484,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR535NOD,https://www.encodeproject.org/files/ENCFF086PSD/@@download/ENCFF086PSD.bed.gz,dendritic_cell_12
ENCFF371BNX,1892666,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR544YXV,https://www.encodeproject.org/files/ENCFF371BNX/@@download/ENCFF371BNX.bed.gz,dendritic_cell_13
ENCFF128JGS,1131583,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR566LZT,https://www.encodeproject.org/files/ENCFF128JGS/@@download/ENCFF128JGS.bed.gz,dendritic_cell_14
ENCFF793JDF,3086767,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR589ROM,https://www.encodeproject.org/files/ENCFF793JDF/@@download/ENCFF793JDF.bed.gz,dendritic_cell_15
ENCFF196XLA,2791350,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR605EGR,https://www.encodeproject.org/files/ENCFF196XLA/@@download/ENCFF196XLA.bed.gz,dendritic_cell_16
ENCFF796SPZ,1452360,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR628SXX,https://www.encodeproject.org/files/ENCFF796SPZ/@@download/ENCFF796SPZ.bed.gz,dendritic_cell_17
ENCFF649JWH,3258393,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR648JJT,https://www.encodeproject.org/files/ENCFF649JWH/@@download/ENCFF649JWH.bed.gz,dendritic_cell_18
ENCFF295ISW,1250828,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR679NRJ,https://www.encodeproject.org/files/ENCFF295ISW/@@download/ENCFF295ISW.bed.gz,dendritic_cell_19
ENCFF497YGE,1372086,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR901IEQ,https://www.encodeproject.org/files/ENCFF497YGE/@@download/ENCFF497YGE.bed.gz,dendritic_cell_20
ENCFF430TYY,2041856,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR904UKR,https://www.encodeproject.org/files/ENCFF430TYY/@@download/ENCFF430TYY.bed.gz,dendritic_cell_21
ENCFF831PZH,2381749,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR926GDT,https://www.encodeproject.org/files/ENCFF831PZH/@@download/ENCFF831PZH.bed.gz,dendritic_cell_22
ENCFF115TFH,1997819,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR926TFY,https://www.encodeproject.org/files/ENCFF115TFH/@@download/ENCFF115TFH.bed.gz,dendritic_cell_23
ENCFF555CWF,800434,GRCh38,dendritic cell,in vitro differentiated cells,"Manuel Garber, UMass",ENCSR940QJU,https://www.encodeproject.org/files/ENCFF555CWF/@@download/ENCFF555CWF.bed.gz,dendritic_cell_24
ENCFF114ZSN,3973491,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR158XTU,https://www.encodeproject.org/files/ENCFF114ZSN/@@download/ENCFF114ZSN.bed.gz,foreskin_keratinocyte_1
ENCFF720EEO,3695043,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR158XTU,https://www.encodeproject.org/files/ENCFF720EEO/@@download/ENCFF720EEO.bed.gz,foreskin_keratinocyte_2
ENCFF971JNS,2898465,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR158XTU,https://www.encodeproject.org/files/ENCFF971JNS/@@download/ENCFF971JNS.bed.gz,foreskin_keratinocyte_3
ENCFF023XMG,3885919,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR245LNF,https://www.encodeproject.org/files/ENCFF023XMG/@@download/ENCFF023XMG.bed.gz,foreskin_keratinocyte_4
ENCFF029PPA,3483218,GRCh38,foreskin keratinocyte,primary cell,"Will Greenleaf, Stanford",ENCSR245LNF,https://www.encodeproject.org/files/ENCFF029PPA/@@download/ENCFF029PPA.bed.gz,foreskin_keratinocyte_5

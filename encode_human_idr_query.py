#!/usr/bin/env python3
"""
Query ENCODE for human narrowPeak IDR files with detailed information.
"""

import requests
import json
import pandas as pd
import argparse


def query_encode_human_idr(assay_filter=None, limit=500):
    """
    Query ENCODE for human narrowPeak IDR files.
    """
    print("Querying ENCODE for human narrowPeak IDR files...")
    
    # Query for human files only
    url = "https://www.encodeproject.org/search/"
    params = {
        "type": "File",
        "file_format": "bed",
        "output_type": "IDR thresholded peaks",
        "status": "released",
        "assembly": "GRCh38",  # Human genome
        "limit": limit,
        "format": "json"
    }
    
    if assay_filter:
        params["assay_title"] = assay_filter
    
    try:
        response = requests.get(url, params=params, timeout=60)
        print(f"Request URL: {response.url}")
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            files = data.get("@graph", [])
            total = data.get("total", len(files))
            print(f"Found {len(files)} files (total available: {total})")
            
            return files, total
            
        else:
            print(f"Error: HTTP {response.status_code}")
            print(response.text)
            return [], 0
            
    except Exception as e:
        print(f"Error: {e}")
        return [], 0


def get_detailed_file_info(files):
    """
    Get detailed information for each file by querying individual file pages.
    """
    print("Getting detailed file information...")
    
    detailed_files = []
    
    for i, file_info in enumerate(files):
        if i % 10 == 0:
            print(f"  Processing file {i+1}/{len(files)}...")
        
        try:
            accession = file_info.get("accession", "")
            
            # Get detailed info from individual file page
            file_url = f"https://www.encodeproject.org/files/{accession}/?format=json"
            response = requests.get(file_url, timeout=30)
            
            if response.status_code == 200:
                detailed_info = response.json()
                
                # Extract comprehensive information
                file_data = {
                    "accession": accession,
                    "file_format": detailed_info.get("file_format", ""),
                    "file_format_type": detailed_info.get("file_format_type", ""),
                    "output_type": detailed_info.get("output_type", ""),
                    "file_size": detailed_info.get("file_size", 0),
                    "assembly": detailed_info.get("assembly", ""),
                    "status": detailed_info.get("status", ""),
                    "date_created": detailed_info.get("date_created", ""),
                    "lab": "",
                    "assay_title": "",
                    "biosample_term_name": "",
                    "biosample_type": "",
                    "biosample_summary": "",
                    "target_label": "",
                    "dataset_accession": "",
                    "download_url": f"https://www.encodeproject.org/files/{accession}/@@download/{accession}.bed.gz"
                }
                
                # Extract dataset information
                dataset_path = detailed_info.get("dataset", "")
                if dataset_path:
                    if isinstance(dataset_path, str):
                        dataset_accession = dataset_path.split("/")[-2] if "/" in dataset_path else dataset_path
                        file_data["dataset_accession"] = dataset_accession
                        
                        # Get dataset details
                        dataset_url = f"https://www.encodeproject.org{dataset_path}?format=json"
                        dataset_response = requests.get(dataset_url, timeout=30)
                        
                        if dataset_response.status_code == 200:
                            dataset_info = dataset_response.json()
                            
                            file_data["assay_title"] = dataset_info.get("assay_title", "")
                            
                            # Lab information
                            lab = dataset_info.get("lab", {})
                            if isinstance(lab, dict):
                                file_data["lab"] = lab.get("title", "")
                            
                            # Biosample information
                            biosample_ontology = dataset_info.get("biosample_ontology", {})
                            file_data["biosample_term_name"] = biosample_ontology.get("term_name", "")
                            file_data["biosample_type"] = biosample_ontology.get("classification", "")
                            
                            # Biosample summary
                            file_data["biosample_summary"] = dataset_info.get("biosample_summary", "")
                            
                            # Target information (for ChIP-seq)
                            target = dataset_info.get("target", {})
                            if isinstance(target, dict):
                                file_data["target_label"] = target.get("label", "")
                
                detailed_files.append(file_data)
                
            else:
                print(f"    Warning: Could not get details for {accession}")
                
        except Exception as e:
            print(f"    Error processing {accession}: {e}")
            continue
    
    return detailed_files


def create_download_resources(df, output_prefix="encode_human_idr"):
    """
    Create download scripts and summaries.
    """
    if len(df) == 0:
        print("No files to process")
        return
    
    # Save detailed file list
    csv_file = f"{output_prefix}_files.csv"
    df.to_csv(csv_file, index=False)
    print(f"Saved detailed file list to: {csv_file}")
    
    # Create download script
    script_file = f"{output_prefix}_download.sh"
    with open(script_file, 'w') as f:
        f.write("#!/bin/bash\n")
        f.write("# ENCODE Human IDR narrowPeak download script\n")
        f.write(f"# Generated for {len(df)} files\n")
        f.write("# Usage: bash encode_human_idr_download.sh\n\n")
        
        f.write("mkdir -p encode_downloads\ncd encode_downloads\n\n")
        
        for _, row in df.iterrows():
            f.write(f"echo 'Downloading {row['accession']} ({row['biosample_term_name']})...'\n")
            f.write(f"wget -O {row['accession']}.bed.gz '{row['download_url']}'\n\n")
    
    print(f"Saved download script to: {script_file}")
    
    # Create URL list
    url_file = f"{output_prefix}_urls.txt"
    with open(url_file, 'w') as f:
        for _, row in df.iterrows():
            f.write(f"{row['download_url']}\n")
    
    print(f"Saved URL list to: {url_file}")
    
    # Print summary statistics
    print(f"\n{'='*60}")
    print(f"ENCODE Human IDR narrowPeak Files Summary")
    print(f"{'='*60}")
    print(f"Total files: {len(df)}")
    print(f"Total size: {df['file_size'].sum() / (1024**3):.2f} GB")
    
    print(f"\nAssay distribution:")
    assay_counts = df['assay_title'].value_counts()
    for assay, count in assay_counts.head(10).items():
        print(f"  {assay}: {count}")
    
    print(f"\nTop biosamples:")
    biosample_counts = df['biosample_term_name'].value_counts()
    for biosample, count in biosample_counts.head(15).items():
        print(f"  {biosample}: {count}")
    
    print(f"\nLab distribution:")
    lab_counts = df['lab'].value_counts()
    for lab, count in lab_counts.head(5).items():
        print(f"  {lab}: {count}")
    
    # Show sample data
    print(f"\nSample of files:")
    sample_cols = ['accession', 'assay_title', 'biosample_term_name', 'target_label']
    print(df[sample_cols].head(10).to_string(index=False))


def main():
    parser = argparse.ArgumentParser(description='Query ENCODE for human IDR narrowPeak files')
    parser.add_argument('--assay', help='Filter by assay (e.g., "ATAC-seq", "ChIP-seq")')
    parser.add_argument('--limit', type=int, default=500, help='Maximum files to retrieve (default: 500)')
    parser.add_argument('--output-prefix', default='encode_human_idr', help='Output file prefix')
    
    args = parser.parse_args()
    
    # Query ENCODE
    files, total = query_encode_human_idr(args.assay, args.limit)
    
    if not files:
        print("No files found")
        return
    
    # Get detailed information
    detailed_files = get_detailed_file_info(files)
    
    if not detailed_files:
        print("No detailed file information obtained")
        return
    
    # Create DataFrame
    df = pd.DataFrame(detailed_files)
    
    # Create download resources
    create_download_resources(df, args.output_prefix)
    
    print(f"\nUsage examples:")
    print(f"  # Download all files:")
    print(f"  chmod +x {args.output_prefix}_download.sh")
    print(f"  bash {args.output_prefix}_download.sh")
    print(f"  ")
    print(f"  # Download specific files using wget:")
    print(f"  head -5 {args.output_prefix}_urls.txt | xargs -n 1 wget")


if __name__ == "__main__":
    main()

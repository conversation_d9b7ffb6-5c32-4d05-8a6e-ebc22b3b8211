#!/usr/bin/env python3
"""
Simple script to process ENCODE BED file to create 10kb genomic windows.
"""

import pandas as pd
import numpy as np


def process_encode_bed_simple(bed_file, window_size=10000):
    """
    Process ENCODE BED file to create genomic windows.
    """
    print(f"Processing ENCODE BED file: {bed_file}")
    
    # Read the file, skipping the header
    print("Loading peaks...")
    peaks_df = pd.read_csv(bed_file, sep='\t', skiprows=1, header=None)
    
    # Assign column names based on narrowPeak format
    peaks_df.columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 'signalValue', 'pValue', 'qValue', 'peak']
    
    print(f"Loaded {len(peaks_df)} peaks")
    
    # Convert to numeric, handling '.' values
    peaks_df['start'] = pd.to_numeric(peaks_df['start'])
    peaks_df['end'] = pd.to_numeric(peaks_df['end'])
    
    # Use pValue as signal (column 7, which is more reliable than signalValue with '.')
    peaks_df['signal'] = pd.to_numeric(peaks_df['pValue'], errors='coerce')
    
    # Drop rows with missing data
    peaks_df = peaks_df.dropna(subset=['start', 'end', 'signal'])
    
    print(f"After cleaning: {len(peaks_df)} peaks")
    print(f"Signal range: {peaks_df['signal'].min():.3f} - {peaks_df['signal'].max():.3f}")
    
    # Filter to main chromosomes
    main_chrs = [f'chr{i}' for i in range(1, 23)] + ['chrX', 'chrY']
    peaks_df = peaks_df[peaks_df['chr'].isin(main_chrs)]
    print(f"Main chromosomes only: {len(peaks_df)} peaks")
    
    # Create genomic windows template
    print("Creating genomic windows...")
    chr_lengths = {
        'chr1': 248956422, 'chr2': 242193529, 'chr3': 198295559, 'chr4': 190214555,
        'chr5': 181538259, 'chr6': 170805979, 'chr7': 159345973, 'chr8': 145138636,
        'chr9': 138394717, 'chr10': 133797422, 'chr11': 135086622, 'chr12': 133275309,
        'chr13': 114364328, 'chr14': 107043718, 'chr15': 101991189, 'chr16': 90338345,
        'chr17': 83257441, 'chr18': 80373285, 'chr19': 58617616, 'chr20': 64444167,
        'chr21': 46709983, 'chr22': 50818468, 'chrX': 156040895, 'chrY': 57227415
    }
    
    windows_list = []
    for chr_name, max_pos in chr_lengths.items():
        starts = list(range(1, max_pos + 1, window_size))
        for start in starts:
            end = min(start + window_size - 1, max_pos)
            windows_list.append({
                'chr': chr_name,
                'start': start,
                'end': end,
                'CA_signal': 0.0
            })
    
    windows_df = pd.DataFrame(windows_list)
    print(f"Created {len(windows_df)} windows")
    
    # Compute overlaps
    print("Computing overlaps...")
    for chr_name in windows_df['chr'].unique():
        print(f"  Processing {chr_name}...")
        
        chr_peaks = peaks_df[peaks_df['chr'] == chr_name]
        chr_windows_mask = windows_df['chr'] == chr_name
        
        if len(chr_peaks) == 0:
            continue
        
        for idx in windows_df[chr_windows_mask].index:
            window = windows_df.loc[idx]
            
            # Find overlapping peaks
            overlapping = chr_peaks[
                (chr_peaks['end'] >= window['start']) & 
                (chr_peaks['start'] <= window['end'])
            ]
            
            if len(overlapping) > 0:
                windows_df.loc[idx, 'CA_signal'] = overlapping['signal'].mean()
    
    # Save results
    output_file = f"ENCFF135EYC_windows_10kb.tsv"
    print(f"Saving to {output_file}...")
    windows_df.to_csv(output_file, sep='\t', index=False)
    
    # Summary
    print(f"\nSummary:")
    print(f"Total windows: {len(windows_df)}")
    print(f"Windows with signal > 0: {(windows_df['CA_signal'] > 0).sum()}")
    print(f"Mean CA signal: {windows_df['CA_signal'].mean():.4f}")
    print(f"Max CA signal: {windows_df['CA_signal'].max():.4f}")
    
    print(f"\nFirst 10 rows:")
    print(windows_df.head(10))
    
    return output_file


if __name__ == "__main__":
    process_encode_bed_simple("ENCFF135EYC.bed")

#!/usr/bin/env python3
"""
Add ENCODE sample to TCGA matrix.
"""

import pandas as pd


def add_encode_to_tcga_matrix():
    """
    Add ENCODE sample to existing TCGA matrix.
    """
    print("Adding ENCODE sample to TCGA matrix...")
    
    # Load ENCODE windows
    print("Loading ENCODE windows...")
    encode_df = pd.read_csv("ENCFF135EYC_windows_10kb.tsv", sep='\t')
    
    # Load TCGA matrix
    print("Loading TCGA matrix (this may take a moment)...")
    tcga_df = pd.read_csv("TCGA_all_samples_combined_10kb.tsv", sep='\t')
    
    print(f"TCGA matrix: {len(tcga_df)} windows × {len(tcga_df.columns)} columns")
    print(f"ENCODE data: {len(encode_df)} windows")
    
    # Verify they have the same windows
    if len(tcga_df) != len(encode_df):
        print("Warning: Different number of windows!")
    
    # Check if coordinates match
    coords_match = (
        (tcga_df['chr'] == encode_df['chr']).all() and
        (tcga_df['start'] == encode_df['start']).all() and
        (tcga_df['end'] == encode_df['end']).all()
    )
    
    if coords_match:
        print("✓ Window coordinates match perfectly!")
        # Simply add the ENCODE column
        tcga_df['ENCODE_ENCFF135EYC'] = encode_df['CA_signal']
    else:
        print("Window coordinates don't match exactly. Using merge approach...")
        # Create merge keys
        encode_df['merge_key'] = encode_df['chr'] + ':' + encode_df['start'].astype(str) + '-' + encode_df['end'].astype(str)
        tcga_df['merge_key'] = tcga_df['chr'] + ':' + tcga_df['start'].astype(str) + '-' + tcga_df['end'].astype(str)
        
        # Merge
        tcga_df = pd.merge(
            tcga_df, 
            encode_df[['merge_key', 'CA_signal']].rename(columns={'CA_signal': 'ENCODE_ENCFF135EYC'}),
            on='merge_key',
            how='left'
        )
        
        # Fill missing values and remove merge key
        tcga_df['ENCODE_ENCFF135EYC'] = tcga_df['ENCODE_ENCFF135EYC'].fillna(0)
        tcga_df = tcga_df.drop('merge_key', axis=1)
    
    # Save combined matrix
    output_file = "TCGA_all_samples_with_ENCODE_10kb.tsv"
    print(f"Saving combined matrix to {output_file}...")
    tcga_df.to_csv(output_file, sep='\t', index=False)
    
    print(f"\n✓ Combined matrix created!")
    print(f"Dimensions: {len(tcga_df)} windows × {len(tcga_df.columns)} columns")
    print(f"ENCODE column 'ENCODE_ENCFF135EYC' added")
    print(f"ENCODE signal range: {tcga_df['ENCODE_ENCFF135EYC'].min():.4f} - {tcga_df['ENCODE_ENCFF135EYC'].max():.4f}")
    print(f"ENCODE windows with signal > 0: {(tcga_df['ENCODE_ENCFF135EYC'] > 0).sum()}")
    
    # Show sample of the data
    print(f"\nSample of combined matrix (first 5 rows, last 5 columns):")
    print(tcga_df.iloc[:5, -5:])
    
    return output_file


if __name__ == "__main__":
    add_encode_to_tcga_matrix()

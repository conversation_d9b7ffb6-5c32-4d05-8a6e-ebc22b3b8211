#!/usr/bin/env python3
"""
Batch convert all BigWig files in a directory to narrowPeak format.
"""

import glob
import os
import subprocess
import argparse


def main():
    parser = argparse.ArgumentParser(description='Batch convert BigWig files to narrowPeak format')
    parser.add_argument('--pattern', default='*.bw', help='Pattern to match BigWig files (default: *.bw)')
    parser.add_argument('--threshold', type=float, help='Signal threshold for peak calling (auto if not specified)')
    parser.add_argument('--min-length', type=int, default=50, help='Minimum peak length in bp (default: 50)')
    
    args = parser.parse_args()
    
    # Find all BigWig files
    bw_files = glob.glob(args.pattern)
    
    if not bw_files:
        print(f"No BigWig files found matching pattern: {args.pattern}")
        return
    
    print(f"Found {len(bw_files)} BigWig files:")
    for f in bw_files:
        print(f"  {f}")
    
    # Convert each file
    for bw_file in bw_files:
        print(f"\n{'='*60}")
        print(f"Converting {bw_file}...")
        
        # Build command
        cmd = ['python', 'convert_bw_to_narrowpeak.py', bw_file]
        
        if args.threshold:
            cmd.extend(['--threshold', str(args.threshold)])
        
        cmd.extend(['--min-length', str(args.min_length)])
        
        # Run conversion
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ Conversion successful!")
                print(result.stdout)
            else:
                print("✗ Conversion failed!")
                print(result.stderr)
                
        except Exception as e:
            print(f"✗ Error running conversion: {e}")
    
    print(f"\n{'='*60}")
    print("Batch conversion completed!")
    
    # List all narrowPeak files
    narrowpeak_files = glob.glob("*.narrowPeak")
    print(f"\nGenerated narrowPeak files ({len(narrowpeak_files)}):")
    for f in sorted(narrowpeak_files):
        size = os.path.getsize(f)
        print(f"  {f} ({size:,} bytes)")


if __name__ == "__main__":
    main()

#!/usr/bin/env python3
"""
Python conversion of R code for processing narrowPeak files and computing CA signal per genomic window.
This script processes all .narrowPeak files in the current directory.
"""

import pandas as pd
import glob
import os
from typing import List
import argparse


def create_genomic_windows(chr_max_positions: dict, window_size: int = 1000) -> pd.DataFrame:
    """
    Create genomic windows for all chromosomes.
    
    Args:
        chr_max_positions: Dictionary with chromosome -> max position
        window_size: Size of each window in base pairs
    
    Returns:
        DataFrame with columns: chr, start, end, mutation_count
    """
    windows_list = []
    
    for chr_name, max_pos in chr_max_positions.items():
        # Create windows from 1 to max_pos with step = window_size
        starts = list(range(1, max_pos + 1, window_size))
        
        for start in starts:
            end = min(start + window_size - 1, max_pos)
            windows_list.append({
                'chr': chr_name,
                'start': start,
                'end': end,
                'mutation_count': 0  # Initialize as in original R code
            })
    
    return pd.DataFrame(windows_list)


def read_narrowpeak_files(peak_files: List[str]) -> pd.DataFrame:
    """
    Read and combine multiple narrowPeak files.
    
    Args:
        peak_files: List of narrowPeak file paths
    
    Returns:
        Combined DataFrame with chr, start, end, signalValue columns
    """
    all_peaks = []
    
    for file_path in peak_files:
        print(f"Reading {file_path}...")
        
        # narrowPeak format: chr, start, end, name, score, strand, signalValue, pValue, qValue, peak
        # We need columns 1, 2, 3, 7 (0-indexed: 0, 1, 2, 6)
        try:
            peaks = pd.read_csv(
                file_path,
                sep='\t',
                header=None,
                usecols=[0, 1, 2, 6],  # chr, start, end, signalValue
                names=['chr', 'start', 'end', 'signalValue']
            )
            
            # Convert 0-based half-open to 1-based inclusive (as in R code)
            peaks['start'] = peaks['start'] + 1
            
            all_peaks.append(peaks)
            print(f"  Loaded {len(peaks)} peaks")
            
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            continue
    
    if not all_peaks:
        raise ValueError("No valid narrowPeak files were loaded")
    
    # Combine all peaks
    combined_peaks = pd.concat(all_peaks, ignore_index=True)
    print(f"Total peaks loaded: {len(combined_peaks)}")
    
    return combined_peaks


def compute_overlaps_and_mean_signal(peaks_df: pd.DataFrame, windows_df: pd.DataFrame) -> pd.DataFrame:
    """
    Compute overlaps between peaks and windows, then calculate mean signal per window.
    Uses efficient pandas operations instead of nested loops.

    Args:
        peaks_df: DataFrame with peak data
        windows_df: DataFrame with genomic windows

    Returns:
        windows_df with added CA_signal column
    """
    print("Computing overlaps and mean signals using efficient method...")

    # Initialize CA_signal column
    windows_df = windows_df.copy()
    windows_df['CA_signal'] = 0.0

    # Create a list to store results
    results = []

    # Group by chromosome for efficiency
    for chr_name in windows_df['chr'].unique():
        print(f"  Processing chromosome {chr_name}...")

        chr_peaks = peaks_df[peaks_df['chr'] == chr_name].copy()
        chr_windows = windows_df[windows_df['chr'] == chr_name].copy()

        if len(chr_peaks) == 0:
            # No peaks for this chromosome, keep CA_signal as 0
            chr_windows['CA_signal'] = 0.0
            results.append(chr_windows)
            continue

        # Initialize CA_signal for this chromosome
        chr_windows['CA_signal'] = 0.0

        # Use a more efficient approach: for each window, find overlapping peaks
        for i, (_, window) in enumerate(chr_windows.iterrows()):
            # Find peaks that overlap with this window
            mask = (chr_peaks['end'] >= window['start']) & (chr_peaks['start'] <= window['end'])
            overlapping_peaks = chr_peaks[mask]

            if len(overlapping_peaks) > 0:
                chr_windows.iloc[i, chr_windows.columns.get_loc('CA_signal')] = overlapping_peaks['signalValue'].mean()

        results.append(chr_windows)

    # Combine all chromosomes
    final_result = pd.concat(results, ignore_index=True)
    print("Overlap computation completed.")
    return final_result


def main():
    parser = argparse.ArgumentParser(description='Process narrowPeak files and compute CA signal per genomic window')
    parser.add_argument('--window-size', type=int, default=1000, 
                       help='Window size in base pairs (default: 1000)')
    parser.add_argument('--output', type=str, default='windows_with_CA_signal.tsv',
                       help='Output file name (default: windows_with_CA_signal.tsv)')
    parser.add_argument('--pattern', type=str, default='*.narrowPeak',
                       help='File pattern to match (default: *.narrowPeak)')
    
    args = parser.parse_args()
    
    # Find all narrowPeak files
    peak_files = glob.glob(args.pattern)
    
    if not peak_files:
        print(f"No files found matching pattern: {args.pattern}")
        print("Available files in current directory:")
        for f in os.listdir('.'):
            if f.endswith('.narrowPeak'):
                print(f"  {f}")
        return
    
    print(f"Found {len(peak_files)} narrowPeak files:")
    for f in peak_files:
        print(f"  {f}")
    
    # Read all narrowPeak files
    try:
        peaks_df = read_narrowpeak_files(peak_files)
    except Exception as e:
        print(f"Error loading peak files: {e}")
        return
    
    # Determine chromosome max positions from the peak data
    print("Determining chromosome boundaries...")
    chr_max_positions = peaks_df.groupby('chr')['end'].max().to_dict()
    print(f"Chromosomes found: {list(chr_max_positions.keys())}")
    
    # Create genomic windows
    print(f"Creating genomic windows with size {args.window_size}...")
    windows_df = create_genomic_windows(chr_max_positions, args.window_size)
    print(f"Created {len(windows_df)} windows")
    
    # Compute overlaps and mean signals
    windows_with_signal = compute_overlaps_and_mean_signal(peaks_df, windows_df)
    
    # Display summary statistics
    print("\nSummary statistics:")
    print(f"Total windows: {len(windows_with_signal)}")
    print(f"Windows with signal > 0: {(windows_with_signal['CA_signal'] > 0).sum()}")
    print(f"Mean CA signal: {windows_with_signal['CA_signal'].mean():.4f}")
    print(f"Max CA signal: {windows_with_signal['CA_signal'].max():.4f}")
    
    # Save results
    print(f"\nSaving results to {args.output}...")
    windows_with_signal.to_csv(args.output, sep='\t', index=False)
    print("Done!")
    
    # Display first few rows
    print("\nFirst 10 rows of results:")
    print(windows_with_signal.head(10))


if __name__ == "__main__":
    main()

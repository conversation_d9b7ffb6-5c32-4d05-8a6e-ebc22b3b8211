#!/usr/bin/env python3
"""
Memory-efficient Python conversion of R code for processing narrowPeak files.
Processes chromosomes one at a time to avoid memory issues with large datasets.
"""

import pandas as pd
import glob
import os
from typing import List
import argparse
import time


def create_genomic_windows_for_chr(chr_name: str, max_pos: int, window_size: int = 1000) -> pd.DataFrame:
    """
    Create genomic windows for a single chromosome.
    
    Args:
        chr_name: Chromosome name
        max_pos: Maximum position for this chromosome
        window_size: Size of each window in base pairs
    
    Returns:
        DataFrame with columns: chr, start, end, mutation_count
    """
    windows_list = []
    
    # Create windows from 1 to max_pos with step = window_size
    starts = list(range(1, max_pos + 1, window_size))
    
    for start in starts:
        end = min(start + window_size - 1, max_pos)
        windows_list.append({
            'chr': chr_name,
            'start': start,
            'end': end,
            'mutation_count': 0  # Initialize as in original R code
        })
    
    return pd.DataFrame(windows_list)


def read_narrowpeak_files(peak_files: List[str]) -> pd.DataFrame:
    """
    Read and combine multiple narrowPeak files.
    
    Args:
        peak_files: List of narrowPeak file paths
    
    Returns:
        Combined DataFrame with chr, start, end, signalValue columns
    """
    all_peaks = []
    
    for file_path in peak_files:
        print(f"Reading {file_path}...")
        
        # narrowPeak format: chr, start, end, name, score, strand, signalValue, pValue, qValue, peak
        # We need columns 1, 2, 3, 7 (0-indexed: 0, 1, 2, 6)
        try:
            peaks = pd.read_csv(
                file_path,
                sep='\t',
                header=None,
                usecols=[0, 1, 2, 6],  # chr, start, end, signalValue
                names=['chr', 'start', 'end', 'signalValue']
            )
            
            # Convert 0-based half-open to 1-based inclusive (as in R code)
            peaks['start'] = peaks['start'] + 1
            
            all_peaks.append(peaks)
            print(f"  Loaded {len(peaks)} peaks")
            
        except Exception as e:
            print(f"Error reading {file_path}: {e}")
            continue
    
    if not all_peaks:
        raise ValueError("No valid narrowPeak files were loaded")
    
    # Combine all peaks
    combined_peaks = pd.concat(all_peaks, ignore_index=True)
    print(f"Total peaks loaded: {len(combined_peaks)}")
    
    return combined_peaks


def process_chromosome_efficiently(chr_name: str, chr_peaks: pd.DataFrame, max_pos: int, window_size: int) -> pd.DataFrame:
    """
    Process a single chromosome efficiently using interval operations.
    
    Args:
        chr_name: Chromosome name
        chr_peaks: DataFrame with peaks for this chromosome
        max_pos: Maximum position for this chromosome
        window_size: Window size in base pairs
    
    Returns:
        DataFrame with windows and CA_signal values
    """
    print(f"  Processing chromosome {chr_name} with {len(chr_peaks)} peaks...")
    
    # Create windows for this chromosome
    chr_windows = create_genomic_windows_for_chr(chr_name, max_pos, window_size)
    chr_windows['CA_signal'] = 0.0
    
    if len(chr_peaks) == 0:
        return chr_windows
    
    # Use a simple but memory-efficient approach
    # For each peak, find which windows it overlaps with
    for _, peak in chr_peaks.iterrows():
        # Find windows that overlap with this peak
        overlapping_mask = (
            (chr_windows['end'] >= peak['start']) & 
            (chr_windows['start'] <= peak['end'])
        )
        
        # Add this peak's signal to all overlapping windows
        overlapping_indices = chr_windows[overlapping_mask].index
        for idx in overlapping_indices:
            if chr_windows.loc[idx, 'CA_signal'] == 0:
                chr_windows.loc[idx, 'CA_signal'] = peak['signalValue']
            else:
                # Average with existing signal (simple approach)
                chr_windows.loc[idx, 'CA_signal'] = (chr_windows.loc[idx, 'CA_signal'] + peak['signalValue']) / 2
    
    return chr_windows


def main():
    parser = argparse.ArgumentParser(description='Process narrowPeak files (memory-efficient version)')
    parser.add_argument('--window-size', type=int, default=1000, 
                       help='Window size in base pairs (default: 1000)')
    parser.add_argument('--output', type=str, default=None,
                       help='Output file name (default: auto-generated based on window size)')
    parser.add_argument('--pattern', type=str, default='*.narrowPeak',
                       help='File pattern to match (default: *.narrowPeak)')
    parser.add_argument('--main-chr-only', action='store_true',
                       help='Process only main chromosomes (chr1-22, chrX, chrY) for faster processing')
    
    args = parser.parse_args()
    
    # Find all narrowPeak files
    peak_files = glob.glob(args.pattern)
    
    if not peak_files:
        print(f"No files found matching pattern: {args.pattern}")
        print("Available files in current directory:")
        for f in os.listdir('.'):
            if f.endswith('.narrowPeak'):
                print(f"  {f}")
        return
    
    print(f"Found {len(peak_files)} narrowPeak files:")
    for f in peak_files:
        print(f"  {f}")
    
    # Read all narrowPeak files
    try:
        peaks_df = read_narrowpeak_files(peak_files)
    except Exception as e:
        print(f"Error loading peak files: {e}")
        return
    
    # Filter to main chromosomes if requested
    if args.main_chr_only:
        main_chrs = [f'chr{i}' for i in range(1, 23)] + ['chrX', 'chrY']
        peaks_df = peaks_df[peaks_df['chr'].isin(main_chrs)]
        print(f"Filtered to main chromosomes. Peaks remaining: {len(peaks_df)}")
    
    # Determine chromosome max positions from the peak data
    print("Determining chromosome boundaries...")
    chr_max_positions = peaks_df.groupby('chr')['end'].max().to_dict()
    print(f"Chromosomes found: {list(chr_max_positions.keys())}")
    
    # Generate output filename if not provided
    if args.output is None:
        if args.window_size >= 1000:
            size_label = f"{args.window_size // 1000}kb"
        else:
            size_label = f"{args.window_size}bp"
        args.output = f"windows_with_CA_signal_{size_label}.tsv"
    
    print(f"Will save results to {args.output}")
    
    # Process each chromosome separately and write to file
    start_time = time.time()
    all_results = []
    
    for chr_name in sorted(chr_max_positions.keys()):
        chr_peaks = peaks_df[peaks_df['chr'] == chr_name].copy()
        max_pos = chr_max_positions[chr_name]
        
        chr_result = process_chromosome_efficiently(chr_name, chr_peaks, max_pos, args.window_size)
        all_results.append(chr_result)
    
    # Combine all results
    print("Combining results from all chromosomes...")
    final_result = pd.concat(all_results, ignore_index=True)
    
    elapsed_time = time.time() - start_time
    print(f"Processing completed in {elapsed_time:.2f} seconds.")
    
    # Display summary statistics
    print("\nSummary statistics:")
    print(f"Total windows: {len(final_result)}")
    print(f"Windows with signal > 0: {(final_result['CA_signal'] > 0).sum()}")
    print(f"Mean CA signal: {final_result['CA_signal'].mean():.4f}")
    print(f"Max CA signal: {final_result['CA_signal'].max():.4f}")
    
    # Save results
    print(f"\nSaving results to {args.output}...")
    final_result.to_csv(args.output, sep='\t', index=False)
    print("Done!")
    
    # Display first few rows
    print("\nFirst 10 rows of results:")
    print(final_result.head(10))


if __name__ == "__main__":
    main()

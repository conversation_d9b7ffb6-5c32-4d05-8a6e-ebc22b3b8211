#!/usr/bin/env python3
"""
Monitor ENCODE download and processing progress.
"""

import os
import time
import pandas as pd


def monitor_progress():
    """
    Monitor the progress of ENCODE pipeline.
    """
    print("Monitoring ENCODE pipeline progress...")
    print("Press Ctrl+C to stop monitoring\n")
    
    try:
        while True:
            print(f"{'='*60}")
            print(f"Progress Check - {time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"{'='*60}")
            
            # Check log file
            if os.path.exists("encode_pipeline.log"):
                with open("encode_pipeline.log", "r") as f:
                    lines = f.readlines()
                
                print(f"Log file has {len(lines)} lines")
                
                # Show last few lines
                if lines:
                    print("\nLast 10 log lines:")
                    for line in lines[-10:]:
                        print(f"  {line.strip()}")
            else:
                print("Log file not found yet...")
            
            # Check downloads directory
            if os.path.exists("encode_downloads"):
                downloaded_files = [f for f in os.listdir("encode_downloads") if f.endswith(".bed")]
                print(f"\nDownloaded files: {len(downloaded_files)}")
            else:
                print("\nDownloads directory not created yet...")
            
            # Check mapping file
            if os.path.exists("encode_file_mapping.csv"):
                mapping_df = pd.read_csv("encode_file_mapping.csv")
                print(f"Total files to process: {len(mapping_df)}")
                
                # Show biosample distribution
                if 'biosample_term_name' in mapping_df.columns:
                    biosample_counts = mapping_df['biosample_term_name'].value_counts()
                    print(f"\nTop biosamples:")
                    for biosample, count in biosample_counts.head(5).items():
                        print(f"  {biosample}: {count}")
            
            # Check output files
            output_files = [f for f in os.listdir(".") if f.startswith("ENCODE_all_samples_combined")]
            if output_files:
                print(f"\nOutput files created: {output_files}")
                for output_file in output_files:
                    size = os.path.getsize(output_file) / (1024**2)  # MB
                    print(f"  {output_file}: {size:.1f} MB")
            
            print(f"\nWaiting 60 seconds for next check...")
            time.sleep(60)
            
    except KeyboardInterrupt:
        print("\nMonitoring stopped.")


if __name__ == "__main__":
    monitor_progress()

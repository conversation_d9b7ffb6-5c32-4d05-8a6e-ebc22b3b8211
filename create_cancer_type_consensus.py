#!/usr/bin/env python3
"""
Create cancer-type-specific consensus narrowPeak files and process them.
"""

import pandas as pd
import numpy as np
import os
import subprocess
import argparse
from collections import defaultdict


def create_consensus_narrowpeaks(mapping_file, output_dir="cancer_type_consensus"):
    """
    Create consensus narrowPeak files for each cancer type.
    For cancer types with multiple samples, merge peaks and average signals.
    """
    # Load mapping
    mapping_df = pd.read_csv(mapping_file, sep='\t')
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Group by cancer type
    cancer_groups = mapping_df.groupby('cancer_type')
    
    consensus_files = []
    
    print(f"Creating consensus files for {len(cancer_groups)} cancer types...")
    
    for cancer_type, group in cancer_groups:
        print(f"\nProcessing {cancer_type} ({len(group)} samples)...")
        
        if len(group) == 1:
            # Single sample - just copy and rename
            sample = group.iloc[0]
            input_file = sample['file_path']
            output_file = os.path.join(output_dir, f"consensus_{cancer_type}.narrowPeak")
            
            import shutil
            shutil.copy2(input_file, output_file)
            consensus_files.append(output_file)
            
            print(f"  Copied single sample: {sample['barcode']}")
            
        else:
            # Multiple samples - create true consensus
            output_file = os.path.join(output_dir, f"consensus_{cancer_type}.narrowPeak")
            
            # Load all narrowPeak files for this cancer type
            all_peaks = []
            
            for _, sample in group.iterrows():
                try:
                    peaks = pd.read_csv(
                        sample['file_path'], 
                        sep='\t', 
                        header=None,
                        names=['chr', 'start', 'end', 'name', 'score', 'strand', 
                               'signalValue', 'pValue', 'qValue', 'peak']
                    )
                    peaks['sample'] = sample['barcode']
                    all_peaks.append(peaks)
                    
                except Exception as e:
                    print(f"    Error loading {sample['barcode']}: {e}")
                    continue
            
            if not all_peaks:
                print(f"  No valid samples for {cancer_type}")
                continue
            
            # Combine all peaks
            combined_peaks = pd.concat(all_peaks, ignore_index=True)
            
            # Create consensus by merging overlapping peaks and averaging signals
            consensus_peaks = create_peak_consensus(combined_peaks)
            
            # Save consensus file
            consensus_peaks.to_csv(output_file, sep='\t', header=False, index=False,
                                 columns=['chr', 'start', 'end', 'name', 'score', 'strand', 
                                         'signalValue', 'pValue', 'qValue', 'peak'])
            
            consensus_files.append(output_file)
            
            print(f"  Created consensus with {len(consensus_peaks)} peaks from {len(group)} samples")
    
    return consensus_files


def create_peak_consensus(combined_peaks):
    """
    Create consensus peaks by merging overlapping regions and averaging signals.
    """
    # Sort by chromosome and position
    combined_peaks = combined_peaks.sort_values(['chr', 'start', 'end'])
    
    consensus_list = []
    peak_id = 1
    
    # Process each chromosome separately
    for chr_name in combined_peaks['chr'].unique():
        chr_peaks = combined_peaks[combined_peaks['chr'] == chr_name].copy()
        
        if len(chr_peaks) == 0:
            continue
        
        # Simple approach: use all peaks and average signals for overlapping regions
        # For a more sophisticated approach, you could implement peak merging
        
        # Group by genomic coordinates (allowing some overlap tolerance)
        merged_peaks = []
        
        for _, peak in chr_peaks.iterrows():
            # For simplicity, just keep all peaks and average signal if same coordinates
            existing_peak = None
            for mp in merged_peaks:
                if (mp['chr'] == peak['chr'] and 
                    abs(mp['start'] - peak['start']) <= 100 and 
                    abs(mp['end'] - peak['end']) <= 100):
                    existing_peak = mp
                    break
            
            if existing_peak:
                # Average the signals
                existing_peak['signalValue'] = (existing_peak['signalValue'] + peak['signalValue']) / 2
                existing_peak['score'] = max(existing_peak['score'], peak['score'])
            else:
                # New peak
                merged_peaks.append({
                    'chr': peak['chr'],
                    'start': peak['start'],
                    'end': peak['end'],
                    'name': f"{chr_name}_consensus_{peak_id}",
                    'score': peak['score'],
                    'strand': peak['strand'],
                    'signalValue': peak['signalValue'],
                    'pValue': -1,
                    'qValue': -1,
                    'peak': peak['peak']
                })
                peak_id += 1
        
        consensus_list.extend(merged_peaks)
    
    return pd.DataFrame(consensus_list)


def process_consensus_files(consensus_files, window_size=10000):
    """
    Process consensus narrowPeak files to create genomic windows.
    """
    print(f"\nProcessing {len(consensus_files)} consensus files...")
    
    results = []
    
    for consensus_file in consensus_files:
        # Extract cancer type from filename
        basename = os.path.basename(consensus_file)
        cancer_type = basename.replace('consensus_', '').replace('.narrowPeak', '')
        
        print(f"Processing {cancer_type}...")
        
        # Generate output filename
        if window_size >= 1000:
            size_label = f"{window_size // 1000}kb"
        else:
            size_label = f"{window_size}bp"
        
        output_file = f"consensus_{cancer_type}_windows_{size_label}.tsv"
        
        # Run processing script
        cmd = [
            'python', 'process_narrowpeak_memory_efficient.py',
            '--main-chr-only',
            '--window-size', str(window_size),
            '--pattern', consensus_file,
            '--output', output_file
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                results.append({
                    'cancer_type': cancer_type,
                    'consensus_file': consensus_file,
                    'windows_file': output_file,
                    'status': 'success'
                })
                print(f"  ✓ Created {output_file}")
            else:
                print(f"  ✗ Error processing {cancer_type}: {result.stderr}")
                results.append({
                    'cancer_type': cancer_type,
                    'consensus_file': consensus_file,
                    'windows_file': None,
                    'status': 'failed'
                })
                
        except Exception as e:
            print(f"  ✗ Exception processing {cancer_type}: {e}")
            results.append({
                'cancer_type': cancer_type,
                'consensus_file': consensus_file,
                'windows_file': None,
                'status': 'failed'
            })
    
    return results


def main():
    parser = argparse.ArgumentParser(description='Create cancer-type-specific consensus analyses')
    parser.add_argument('--mapping-file', default='tcga_cancer_type_mapping.tsv',
                       help='Sample mapping file (default: tcga_cancer_type_mapping.tsv)')
    parser.add_argument('--output-dir', default='cancer_type_consensus',
                       help='Output directory for consensus files (default: cancer_type_consensus)')
    parser.add_argument('--window-size', type=int, default=10000,
                       help='Window size for genomic analysis (default: 10000)')
    parser.add_argument('--skip-consensus', action='store_true',
                       help='Skip consensus creation (use existing files)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.mapping_file):
        print(f"Error: Mapping file {args.mapping_file} not found")
        print("Run: python analyze_tcga_cancer_types.py first")
        return
    
    # Create consensus files
    if not args.skip_consensus:
        consensus_files = create_consensus_narrowpeaks(args.mapping_file, args.output_dir)
    else:
        # Find existing consensus files
        import glob
        consensus_files = glob.glob(os.path.join(args.output_dir, "consensus_*.narrowPeak"))
        print(f"Found {len(consensus_files)} existing consensus files")
    
    if not consensus_files:
        print("No consensus files to process")
        return
    
    # Process consensus files to create genomic windows
    results = process_consensus_files(consensus_files, args.window_size)
    
    # Summary
    successful = [r for r in results if r['status'] == 'success']
    failed = [r for r in results if r['status'] == 'failed']
    
    print(f"\n{'='*60}")
    print(f"Cancer-type-specific analysis completed!")
    print(f"Successful: {len(successful)}")
    print(f"Failed: {len(failed)}")
    
    if successful:
        print(f"\nGenerated window files:")
        for result in successful:
            print(f"  {result['cancer_type']}: {result['windows_file']}")
    
    if failed:
        print(f"\nFailed cancer types:")
        for result in failed:
            print(f"  {result['cancer_type']}")


if __name__ == "__main__":
    main()
